#!/bin/bash
# Goldbot Docker部署脚本

set -e

echo "🤖 Goldbot Docker 部署脚本"
echo "=========================="

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 检查必要文件
echo "📋 检查必要文件..."

required_files=("config.yaml" "Dockerfile" "docker-compose.yml")
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ 缺少必要文件: $file"
        exit 1
    fi
    echo "✅ $file"
done

# 检查Telegram会话文件
echo ""
echo "📱 检查Telegram会话文件..."
session_file="telegram_session.session"

if [ -f "$session_file" ]; then
    echo "✅ $session_file ($(stat -f%z "$session_file" 2>/dev/null || stat -c%s "$session_file" 2>/dev/null) bytes)"
    session_count=1
else
    echo "⚠️  $session_file (不存在，Telegram功能将不可用)"
    session_count=0
fi

if [ $session_count -eq 0 ]; then
    echo ""
    echo "⚠️  未找到Telegram会话文件"
    echo "   如需使用Telegram功能，请先运行: python setup_telegram_sessions.py"
    echo "   或者可以跳过，系统的其他功能仍然可用"
    echo ""
    read -p "是否继续部署？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "部署已取消"
        exit 1
    fi
fi

# 创建必要目录
echo ""
echo "📁 创建数据目录..."
mkdir -p data logs config_backups
echo "✅ 数据目录创建完成"

# 停止现有容器
echo ""
echo "🛑 停止现有容器..."
docker-compose down 2>/dev/null || true

# 构建镜像
echo ""
echo "🔨 构建Docker镜像..."
docker-compose build

# 启动服务
echo ""
echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo ""
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo ""
echo "🔍 检查服务状态..."
docker-compose ps

# 检查健康状态
echo ""
echo "🏥 检查服务健康状态..."
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if curl -f http://localhost:8080/api/health >/dev/null 2>&1; then
        echo "✅ 服务健康检查通过"
        break
    else
        echo "⏳ 等待服务启动... ($attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    fi
done

if [ $attempt -gt $max_attempts ]; then
    echo "❌ 服务启动超时，请检查日志"
    echo "查看日志: docker-compose logs goldbot"
    exit 1
fi

# 显示部署信息
echo ""
echo "🎉 部署完成！"
echo "==============="
echo "📊 Web界面: http://localhost:8080"
echo "🔧 管理界面: http://localhost:8080 (点击管理员登录)"
echo "📋 服务状态: docker-compose ps"
echo "📝 查看日志: docker-compose logs -f goldbot"
echo "🛑 停止服务: docker-compose down"
echo ""

# 显示管理员密码提示
if grep -q "admin_password:" config.yaml; then
    echo "🔐 管理员密码已在config.yaml中配置"
else
    echo "⚠️  请在config.yaml中配置管理员密码"
fi

echo ""
echo "✅ Goldbot已成功部署并运行！"
