# 智能交易机器人环境变量配置模板
# 此文件用于Docker部署和自动化脚本
# 包含占位符，由部署脚本自动替换

# ================================
# Binance API配置
# ================================
BINANCE_API_KEY={{BINANCE_API_KEY}}
BINANCE_API_SECRET={{BINANCE_API_SECRET}}
BINANCE_TESTNET={{BINANCE_TESTNET:-true}}

# ================================
# Telegram配置
# ================================
TELEGRAM_API_ID={{TELEGRAM_API_ID}}
TELEGRAM_API_HASH={{TELEGRAM_API_HASH}}
TELEGRAM_PHONE={{TELEGRAM_PHONE}}

# ================================
# 通知配置
# ================================
FEISHU_WEBHOOK_URL={{FEISHU_WEBHOOK_URL}}
FEISHU_WEBHOOK_SIGN={{FEISHU_WEBHOOK_SIGN}}

# ================================
# 交易配置
# ================================
TRADING_AMOUNT={{TRADING_AMOUNT:-100}}
LEVERAGE={{LEVERAGE:-10}}
STOP_LOSS_PERCENT={{STOP_LOSS_PERCENT:-80}}

# ================================
# 系统配置
# ================================
TZ={{TZ:-Asia/Shanghai}}
LOG_LEVEL={{LOG_LEVEL:-INFO}}
DEBUG_MODE={{DEBUG_MODE:-false}}

# ================================
# Web服务配置
# ================================
WEB_HOST={{WEB_HOST:-0.0.0.0}}
WEB_PORT={{WEB_PORT:-8080}}

# ================================
# Docker/容器配置
# ================================
PYTHONPATH={{PYTHONPATH:-/app}}
PYTHONUNBUFFERED={{PYTHONUNBUFFERED:-1}}

# ================================
# 数据存储配置
# ================================
DB_PATH={{DB_PATH:-/app/data}}

# ================================
# 缓存配置（可选）
# ================================
REDIS_URL={{REDIS_URL:-redis://redis:6379/0}}

# ================================
# 安全配置
# ================================
SECRET_KEY={{SECRET_KEY}}
