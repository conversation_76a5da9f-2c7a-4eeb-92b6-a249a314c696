@echo off
chcp 65001 >nul
echo 🤖 Goldbot Docker 部署脚本 (Windows)
echo ==================================

REM 检查Docker是否安装
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker未安装，请先安装Docker Desktop
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker Compose未安装，请先安装Docker Compose
    pause
    exit /b 1
)

REM 检查必要文件
echo 📋 检查必要文件...

if not exist "config.yaml" (
    echo ❌ 缺少必要文件: config.yaml
    pause
    exit /b 1
)
echo ✅ config.yaml

if not exist "Dockerfile" (
    echo ❌ 缺少必要文件: Dockerfile
    pause
    exit /b 1
)
echo ✅ Dockerfile

if not exist "docker-compose.yml" (
    echo ❌ 缺少必要文件: docker-compose.yml
    pause
    exit /b 1
)
echo ✅ docker-compose.yml

REM 检查Telegram会话文件
echo.
echo 📱 检查Telegram会话文件...

set session_count=0

if exist "telegram_session.session" (
    echo ✅ telegram_session.session
    set /a session_count+=1
) else (
    echo ⚠️  telegram_session.session (不存在，Telegram功能将不可用)
)

if %session_count%==0 (
    echo.
    echo ⚠️  未找到Telegram会话文件
    echo    如需使用Telegram功能，请先运行: python setup_telegram_sessions.py
    echo    或者可以跳过，系统的其他功能仍然可用
    echo.
    set /p continue="是否继续部署？(y/N): "
    if /i not "%continue%"=="y" (
        echo 部署已取消
        pause
        exit /b 1
    )
)

REM 创建必要目录
echo.
echo 📁 创建数据目录...
if not exist "data" mkdir data
if not exist "logs" mkdir logs
if not exist "config_backups" mkdir config_backups
echo ✅ 数据目录创建完成

REM 停止现有容器
echo.
echo 🛑 停止现有容器...
docker-compose down 2>nul

REM 构建镜像
echo.
echo 🔨 构建Docker镜像...
docker-compose build
if errorlevel 1 (
    echo ❌ 镜像构建失败
    pause
    exit /b 1
)

REM 启动服务
echo.
echo 🚀 启动服务...
docker-compose up -d
if errorlevel 1 (
    echo ❌ 服务启动失败
    pause
    exit /b 1
)

REM 等待服务启动
echo.
echo ⏳ 等待服务启动...
timeout /t 10 /nobreak >nul

REM 检查服务状态
echo.
echo 🔍 检查服务状态...
docker-compose ps

REM 检查健康状态
echo.
echo 🏥 检查服务健康状态...
set max_attempts=30
set attempt=1

:health_check
curl -f http://localhost:8080/api/health >nul 2>&1
if not errorlevel 1 (
    echo ✅ 服务健康检查通过
    goto deploy_success
)

echo ⏳ 等待服务启动... (%attempt%/%max_attempts%)
timeout /t 2 /nobreak >nul
set /a attempt+=1

if %attempt% leq %max_attempts% goto health_check

echo ❌ 服务启动超时，请检查日志
echo 查看日志: docker-compose logs goldbot
pause
exit /b 1

:deploy_success
REM 显示部署信息
echo.
echo 🎉 部署完成！
echo ===============
echo 📊 Web界面: http://localhost:8080
echo 🔧 管理界面: http://localhost:8080 (点击管理员登录)
echo 📋 服务状态: docker-compose ps
echo 📝 查看日志: docker-compose logs -f goldbot
echo 🛑 停止服务: docker-compose down
echo.

REM 显示管理员密码提示
findstr /c:"admin_password:" config.yaml >nul 2>&1
if not errorlevel 1 (
    echo 🔐 管理员密码已在config.yaml中配置
) else (
    echo ⚠️  请在config.yaml中配置管理员密码
)

echo.
echo ✅ Goldbot已成功部署并运行！
echo.
echo 按任意键打开Web界面...
pause >nul
start http://localhost:8080
