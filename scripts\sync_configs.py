#!/usr/bin/env python3
"""
配置文件同步工具
确保所有配置文件保持一致
"""
import os
import sys
import yaml
import shutil
from pathlib import Path
from typing import Dict, Any
from loguru import logger

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def load_yaml_config(file_path: str) -> Dict[str, Any]:
    """加载YAML配置文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f) or {}
    except Exception as e:
        logger.error(f"加载配置文件失败 {file_path}: {e}")
        return {}

def save_yaml_config(config: Dict[str, Any], file_path: str):
    """保存YAML配置文件"""
    try:
        # 创建目录
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
        logger.info(f"✅ 配置文件已保存: {file_path}")
    except Exception as e:
        logger.error(f"保存配置文件失败 {file_path}: {e}")

def load_env_file(file_path: str) -> Dict[str, str]:
    """加载.env文件"""
    env_vars = {}
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key.strip()] = value.strip()
    except Exception as e:
        logger.error(f"加载环境变量文件失败 {file_path}: {e}")
    return env_vars

def save_env_file(env_vars: Dict[str, str], file_path: str, template_path: str = None):
    """保存.env文件"""
    try:
        lines = []
        
        # 如果有模板文件，保留注释和格式
        if template_path and os.path.exists(template_path):
            with open(template_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line_stripped = line.strip()
                    if line_stripped.startswith('#') or not line_stripped:
                        lines.append(line.rstrip())
                    elif '=' in line_stripped:
                        key = line_stripped.split('=', 1)[0].strip()
                        if key in env_vars:
                            lines.append(f"{key}={env_vars[key]}")
                        else:
                            lines.append(line.rstrip())
        else:
            # 直接写入环境变量
            for key, value in env_vars.items():
                lines.append(f"{key}={value}")
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines) + '\n')
        
        logger.info(f"✅ 环境变量文件已保存: {file_path}")
    except Exception as e:
        logger.error(f"保存环境变量文件失败 {file_path}: {e}")

def extract_config_to_env(config: Dict[str, Any]) -> Dict[str, str]:
    """从配置文件提取环境变量"""
    env_vars = {}
    
    # Binance配置
    binance_config = config.get('trading', {}).get('binance', {})
    if binance_config.get('api_key'):
        env_vars['BINANCE_API_KEY'] = str(binance_config['api_key'])
    if binance_config.get('api_secret'):
        env_vars['BINANCE_API_SECRET'] = str(binance_config['api_secret'])
    if 'testnet' in binance_config:
        env_vars['BINANCE_TESTNET'] = str(binance_config['testnet']).lower()
    
    # Telegram配置
    telegram_config = config.get('monitoring', {}).get('telegram', {})
    if telegram_config.get('api_id'):
        env_vars['TELEGRAM_API_ID'] = str(telegram_config['api_id'])
    if telegram_config.get('api_hash'):
        env_vars['TELEGRAM_API_HASH'] = str(telegram_config['api_hash'])
    if telegram_config.get('phone'):
        env_vars['TELEGRAM_PHONE'] = str(telegram_config['phone'])
    
    # 飞书配置
    feishu_config = config.get('notifications', {}).get('feishu', {})
    if feishu_config.get('webhook_url'):
        env_vars['FEISHU_WEBHOOK_URL'] = str(feishu_config['webhook_url'])
    if feishu_config.get('webhook_sign'):
        env_vars['FEISHU_WEBHOOK_SIGN'] = str(feishu_config['webhook_sign'])
    
    # 交易配置
    fixed_strategy = config.get('fixed_strategy', {})
    if fixed_strategy.get('amount'):
        env_vars['TRADING_AMOUNT'] = str(fixed_strategy['amount'])
    if fixed_strategy.get('leverage'):
        env_vars['LEVERAGE'] = str(fixed_strategy['leverage'])
    
    # Web配置
    web_config = config.get('web', {})
    if web_config.get('port'):
        env_vars['WEB_PORT'] = str(web_config['port'])
    
    return env_vars

def merge_env_to_config(config: Dict[str, Any], env_vars: Dict[str, str]) -> Dict[str, Any]:
    """将环境变量合并到配置中"""
    # 深拷贝配置
    import copy
    merged_config = copy.deepcopy(config)
    
    # 确保必要的配置结构存在
    if 'trading' not in merged_config:
        merged_config['trading'] = {}
    if 'binance' not in merged_config['trading']:
        merged_config['trading']['binance'] = {}
    if 'monitoring' not in merged_config:
        merged_config['monitoring'] = {}
    if 'telegram' not in merged_config['monitoring']:
        merged_config['monitoring']['telegram'] = {}
    if 'notifications' not in merged_config:
        merged_config['notifications'] = {}
    if 'feishu' not in merged_config['notifications']:
        merged_config['notifications']['feishu'] = {}
    
    # 合并Binance配置
    if 'BINANCE_API_KEY' in env_vars:
        merged_config['trading']['binance']['api_key'] = env_vars['BINANCE_API_KEY']
    if 'BINANCE_API_SECRET' in env_vars:
        merged_config['trading']['binance']['api_secret'] = env_vars['BINANCE_API_SECRET']
    if 'BINANCE_TESTNET' in env_vars:
        merged_config['trading']['binance']['testnet'] = env_vars['BINANCE_TESTNET'].lower() == 'true'
    
    # 合并Telegram配置
    if 'TELEGRAM_API_ID' in env_vars:
        merged_config['monitoring']['telegram']['api_id'] = int(env_vars['TELEGRAM_API_ID'])
    if 'TELEGRAM_API_HASH' in env_vars:
        merged_config['monitoring']['telegram']['api_hash'] = env_vars['TELEGRAM_API_HASH']
    if 'TELEGRAM_PHONE' in env_vars:
        merged_config['monitoring']['telegram']['phone'] = env_vars['TELEGRAM_PHONE']
    
    # 合并飞书配置
    if 'FEISHU_WEBHOOK_URL' in env_vars:
        merged_config['notifications']['feishu']['webhook_url'] = env_vars['FEISHU_WEBHOOK_URL']
    if 'FEISHU_WEBHOOK_SIGN' in env_vars:
        merged_config['notifications']['feishu']['webhook_sign'] = env_vars['FEISHU_WEBHOOK_SIGN']
    
    return merged_config

def sync_configs():
    """同步所有配置文件"""
    logger.info("🔄 开始同步配置文件...")
    
    # 配置文件路径
    main_config = "config.yaml"
    example_config = "configs/config_with_feishu.yaml"
    env_file = ".env"
    env_example = ".env.example"
    env_template = ".env.template"
    
    # 1. 加载主配置文件
    logger.info("📋 加载主配置文件...")
    config = load_yaml_config(main_config)
    if not config:
        logger.error("❌ 主配置文件为空或无法加载")
        return False
    
    # 2. 加载环境变量
    logger.info("🔧 加载环境变量...")
    env_vars = load_env_file(env_file) if os.path.exists(env_file) else {}
    
    # 3. 合并配置
    logger.info("🔀 合并配置...")
    merged_config = merge_env_to_config(config, env_vars)
    
    # 4. 更新示例配置文件
    logger.info("📝 更新示例配置文件...")
    example_config_data = load_yaml_config(example_config)
    
    # 保留示例配置的结构，但更新关键配置
    if example_config_data:
        # 更新固定策略配置
        if 'fixed_strategy' in merged_config:
            example_config_data['fixed_strategy'] = merged_config['fixed_strategy']
        
        # 更新监控配置结构（但保留示例值）
        if 'monitoring' in merged_config:
            if 'monitoring' not in example_config_data:
                example_config_data['monitoring'] = {}
            if 'telegram' not in example_config_data['monitoring']:
                example_config_data['monitoring']['telegram'] = {}
            
            # 保留关键词等配置
            if 'keywords' in merged_config['monitoring'].get('telegram', {}):
                example_config_data['monitoring']['telegram']['keywords'] = merged_config['monitoring']['telegram']['keywords']
    
    save_yaml_config(example_config_data, example_config)
    
    # 5. 更新环境变量文件
    logger.info("🌍 更新环境变量文件...")
    extracted_env = extract_config_to_env(merged_config)
    
    # 合并现有环境变量
    final_env = {**extracted_env, **env_vars}
    
    # 保存环境变量文件
    save_env_file(final_env, env_file, env_template)
    
    # 6. 创建备份
    logger.info("💾 创建配置备份...")
    backup_dir = "backup_configs"
    os.makedirs(backup_dir, exist_ok=True)
    
    from datetime import datetime
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    if os.path.exists(main_config):
        shutil.copy2(main_config, f"{backup_dir}/config_{timestamp}.yaml")
    if os.path.exists(env_file):
        shutil.copy2(env_file, f"{backup_dir}/.env_{timestamp}")
    
    logger.info("✅ 配置文件同步完成！")
    logger.info("\n📋 同步摘要:")
    logger.info(f"  • 主配置: {main_config}")
    logger.info(f"  • 示例配置: {example_config}")
    logger.info(f"  • 环境变量: {env_file}")
    logger.info(f"  • 备份目录: {backup_dir}")
    
    return True

def check_config_consistency():
    """检查配置一致性"""
    logger.info("🔍 检查配置一致性...")
    
    # 加载配置
    config = load_yaml_config("config.yaml")
    env_vars = load_env_file(".env") if os.path.exists(".env") else {}
    
    issues = []
    
    # 检查关键配置
    binance_config = config.get('trading', {}).get('binance', {})
    
    # 检查API密钥一致性
    if binance_config.get('api_key') != env_vars.get('BINANCE_API_KEY'):
        issues.append("Binance API Key 不一致")
    
    if binance_config.get('api_secret') != env_vars.get('BINANCE_API_SECRET'):
        issues.append("Binance API Secret 不一致")
    
    # 检查Telegram配置
    telegram_config = config.get('monitoring', {}).get('telegram', {})
    if str(telegram_config.get('api_id', '')) != env_vars.get('TELEGRAM_API_ID', ''):
        issues.append("Telegram API ID 不一致")
    
    if issues:
        logger.warning("⚠️ 发现配置不一致:")
        for issue in issues:
            logger.warning(f"  • {issue}")
        return False
    else:
        logger.info("✅ 配置一致性检查通过")
        return True

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="配置文件同步工具")
    parser.add_argument('--check', action='store_true', help='仅检查配置一致性')
    parser.add_argument('--sync', action='store_true', help='同步配置文件')
    
    args = parser.parse_args()
    
    if args.check:
        check_config_consistency()
    elif args.sync:
        sync_configs()
    else:
        # 默认先检查再同步
        if not check_config_consistency():
            logger.info("配置不一致，开始同步...")
            sync_configs()
        else:
            logger.info("配置已同步，无需操作")
