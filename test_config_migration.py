#!/usr/bin/env python3
"""
配置迁移验证测试
验证系统是否能正常从config.yaml读取所有配置
"""
import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

from src.utils import load_config
from src.core.config_manager import ConfigManager
import asyncio

def test_basic_config_loading():
    """测试基本配置加载"""
    print("🔍 测试基本配置加载...")
    
    config = load_config()
    assert config, "配置文件加载失败"
    
    # 检查关键配置项
    assert config.get('trading', {}).get('binance', {}).get('api_key'), "缺少Binance API Key"
    assert config.get('trading', {}).get('binance', {}).get('api_secret'), "缺少Binance API Secret"
    assert config.get('monitoring', {}).get('telegram', {}).get('api_id'), "缺少Telegram API ID"
    assert config.get('monitoring', {}).get('telegram', {}).get('api_hash'), "缺少Telegram API Hash"
    assert config.get('notifications', {}).get('feishu', {}).get('webhook_url'), "缺少飞书Webhook URL"
    
    print("✅ 基本配置加载测试通过")

async def test_config_manager():
    """测试配置管理器"""
    print("🔍 测试配置管理器...")
    
    cm = ConfigManager()
    await cm.initialize()
    
    assert cm.config, "配置管理器配置为空"
    assert len(cm.config) > 0, "配置管理器配置项为空"
    
    # 测试配置获取
    api_key = cm.get_config_value('trading.binance.api_key')
    assert api_key, "无法获取Binance API Key"
    
    await cm.shutdown()
    print("✅ 配置管理器测试通过")

def test_no_env_dependencies():
    """测试确保没有环境变量依赖"""
    print("🔍 测试环境变量依赖清理...")
    
    # 检查关键文件是否存在
    assert not os.path.exists('.env'), ".env文件仍然存在"
    assert not os.path.exists('.env.example'), ".env.example文件仍然存在"
    assert not os.path.exists('.env.template'), ".env.template文件仍然存在"
    
    # 检查requirements.txt
    with open('requirements.txt', 'r', encoding='utf-8') as f:
        requirements = f.read()
        assert 'python-dotenv' not in requirements, "requirements.txt仍包含python-dotenv依赖"
    
    print("✅ 环境变量依赖清理测试通过")

async def main():
    """主测试函数"""
    print("🚀 开始配置迁移验证测试...")
    print("=" * 50)
    
    try:
        # 基本配置加载测试
        test_basic_config_loading()
        
        # 配置管理器测试
        await test_config_manager()
        
        # 环境变量依赖清理测试
        test_no_env_dependencies()
        
        print("=" * 50)
        print("🎉 所有测试通过！配置迁移成功完成！")
        print("\n📋 迁移摘要:")
        print("  ✅ 移除了.env配置文件")
        print("  ✅ 移除了环境变量加载逻辑")
        print("  ✅ 简化了配置初始化流程")
        print("  ✅ 更新了配置同步脚本")
        print("  ✅ 移除了python-dotenv依赖")
        print("  ✅ 所有配置现在统一从config.yaml读取")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
