# 智能交易机器人环境变量配置示例
# 复制此文件为 .env 并填入您的真实配置值

# ================================
# 必填配置 - 系统运行必需
# ================================

# Binance API配置 (必填)
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_API_SECRET=your_binance_api_secret_here
BINANCE_TESTNET=true

# Telegram API配置 (必填)
TELEGRAM_API_ID=your_telegram_api_id
TELEGRAM_API_HASH=your_telegram_api_hash
TELEGRAM_PHONE=+1234567890

# ================================
# 可选配置 - 根据需要填写
# ================================

# 飞书通知配置 (可选)
FEISHU_WEBHOOK_URL=https://open.feishu.cn/open-apis/bot/v2/hook/your-webhook-id
FEISHU_WEBHOOK_SIGN=your_webhook_sign

# 交易配置
TRADING_AMOUNT=100
LEVERAGE=10
STOP_LOSS_PERCENT=80

# Web界面配置
WEB_HOST=0.0.0.0
WEB_PORT=8080

# 系统配置
LOG_LEVEL=INFO
DEBUG_MODE=false
TZ=Asia/Shanghai

# ================================
# 高级配置 - 通常不需要修改
# ================================

# 数据库配置
DB_PATH=./data

# 缓存配置 (可选)
REDIS_URL=redis://localhost:6379/0

# 安全配置
SECRET_KEY=your_secret_key_here
